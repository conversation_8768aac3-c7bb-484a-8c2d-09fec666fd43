from faker import Faker
import os
import gzip
import random
from datetime import datetime
import minio
fake = Faker('zh_CN')

def generate_mgtv_log_line():
    """生成一行MGTV日志数据"""
    # remote_addr：用户IP
    remote_addr = fake.ipv4()

    # remote_user：通常为 -
    remote_user = "-"

    # time_local：时间戳
    time_local = datetime.now().strftime("%d/%b/%Y:%H:%M:%S +0800")

    # request：请求URL和协议
    video_types = ["dianshiju", "zongyi", "dianying", "dongman"]
    video_type = random.choice(video_types)
    video_id = fake.random_int(min=10000, max=99999)
    file_hash = fake.uuid4().replace('-', '').upper()
    date_str = fake.date_between(start_date='-1y', end_date='today').strftime('%Y%m%d')
    segment_start = random.randint(1000000, 9000000)
    segment_end = segment_start + random.randint(10000, 100000)
    uuid = fake.uuid4().replace('-', '')

    request_url = f"http://pcvideo.mgtv.com/mp4/2020/{video_type}/xfsn_{video_id}/{file_hash}_{date_str}_1_1_383.mp4/{segment_start}_{segment_end}_v01_mp4.ts?uuid={uuid}"
    request = f"GET {request_url} HTTP/1.1"

    # status：状态码
    status_codes = [200, 206, 304, 404, 500]
    status_weights = [80, 10, 5, 3, 2]  # 200状态码占大部分
    status = random.choices(status_codes, weights=status_weights)[0]

    # bytes_sent：发送数据大小（包含响应头）
    body_size = random.randint(100000, 2000000)
    header_size = random.randint(200, 1000)
    bytes_sent = body_size + header_size

    # body_bytes_sent：发送数据大小（不包含响应头大小）
    body_bytes_sent = body_size

    # request_time：响应处理时间(ms)
    request_time = random.randint(100, 10000)

    # http_range：通常为空
    http_range = '""'

    # http_referer：通常为空
    http_referer = '""'

    # http_user_agent：用户代理
    user_agents = [
        "AppleCoreMedia/1.0.0.12F69 (iPad; U; CPU OS 8_3 like Mac OS X; zh_cn)",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    ]
    http_user_agent = f'"{random.choice(user_agents)}"'

    # http_x_forwarded_for：通常为 -
    http_x_forwarded_for = '"-"'

    # connection：连接ID
    connection = str(random.randint(100000000, 999999999))

    # auth_ret：认证结果
    auth_ret = random.choice([0, 1])

    # PNO：请求串中的pno字段
    pno = random.randint(1000, 9999)

    # HIT：命中状态
    hit_status = random.choices(["HIT", "MISS"], weights=[70, 30])[0]  # HIT占大部分
    hit = f'"{hit_status}"'

    # server_addr：服务器IP
    server_addr = fake.ipv4()

    # 组装日志行
    log_line = f"{remote_addr} - {remote_user} [{time_local}] \"{request}\" {status} {bytes_sent} {body_bytes_sent} {request_time} {http_range} {http_referer} {http_user_agent} {http_x_forwarded_for} \"{connection}\" {auth_ret} {pno} {hit} {server_addr}"

    return log_line

def generate_mgtv_logs(num_lines=10000):
    """生成MGTV日志数据并压缩成gzip包"""
    gzip_filename = 'mgtv_access.log.gz'

    # 生成日志数据并直接写入gzip文件
    with gzip.open(gzip_filename, 'wt', encoding='utf-8') as gzip_file:
        for i in range(num_lines):
            log_line = generate_mgtv_log_line()
            gzip_file.write(log_line + '\n')

    print(f"日志数据生成完成！")
    print(f"文件名：{gzip_filename}")
    print(f"文件大小：{os.path.getsize(gzip_filename)} 字节")

    return gzip_filename

# 生成日志数据
if __name__ == "__main__":
    gzip_filename = generate_mgtv_logs(1000000)

    # 上传到MinIO（可选）
    minioClient = minio.Minio('localhost:9000', access_key='minioadmin', secret_key='minioadmin', secure=False)
    minioClient.put_object("mgtv-ct", f"skywalking.xin/2025-07-31/2025-07-31_15:00.gz",
                          open(gzip_filename, 'rb'), os.path.getsize(gzip_filename))
