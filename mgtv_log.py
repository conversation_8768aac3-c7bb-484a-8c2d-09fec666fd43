from faker import Faker
import minio
import os
import csv

fake = Faker()
fileName = 'data.csv'
with open("", "w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file, delimiter=" ")
    for i in range(10000):
        row = []
        row.append(fake.bs())
        writer.writerow(row)

minioClient = minio.Minio('localhost:9000', access_key='minioadmin', secret_key='minioadmin', secure=False)

minioClient.append_object