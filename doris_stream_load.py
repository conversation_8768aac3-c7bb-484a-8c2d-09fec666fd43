#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import csv
import random
import argparse
import logging
import requests
import datetime
import base64
from faker import Faker
from typing import List, Any

# 禁用Faker的日志
logging.getLogger("faker").setLevel(logging.WARNING)

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 使用DEBUG级别以显示更多信息
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class DorisTableInfo:
    """Doris表信息类，用于存储表结构信息"""

    def __init__(self, host, port, user, password, database, table, mysql_port=9030):
        self.host = host
        self.port = port  # HTTP端口
        self.mysql_port = mysql_port  # MySQL协议端口
        self.user = user
        self.password = password
        self.database = database
        self.table = table
        self.columns = []
        self.column_types = {}
        self.fetch_table_schema()

    def fetch_table_schema(self):
        """从Doris获取表结构信息"""
        logger.info(f"尝试获取表 {self.database}.{self.table} 的结构信息...")

        # 使用MySQL连接方式获取表结构
        try:
            self._fetch_using_mysql_connection()
            if self.columns:
                logger.info(
                    f"成功使用MySQL连接获取表 {self.database}.{self.table} 的结构信息，共 {len(self.columns)} 列"
                )
                return
        except Exception as e:
            logger.warning(f"使用MySQL连接获取表结构失败: {str(e)}")

    def _fetch_using_mysql_connection(self):
        """使用MySQL连接方式获取表结构"""
        logger.info(
            f"尝试使用MySQL连接获取表 {self.database}.{self.table} 的结构信息..."
        )

        try:
            # 尝试导入pymysql
            import pymysql
        except ImportError:
            logger.warning("未安装pymysql模块，请使用pip install pymysql安装")
            raise ImportError("未安装pymysql模块，请使用pip install pymysql安装")

        try:
            # 建立MySQL连接
            conn = pymysql.connect(
                host=self.host,
                port=self.mysql_port,
                user=self.user,
                password=self.password,
                database=self.database,
                connect_timeout=10,
            )

            logger.info(f"成功连接到Doris MySQL端口: {self.host}:{self.mysql_port}")

            try:
                with conn.cursor() as cursor:
                    # 使用DESCRIBE命令获取表结构
                    sql = f"DESCRIBE `{self.table}`"
                    logger.debug(f"执行SQL: {sql}")
                    cursor.execute(sql)
                    results = cursor.fetchall()

                    # 解析结果
                    if results:
                        for row in results:
                            # DESCRIBE命令返回的结果通常是：字段名、类型、是否允许NULL、键类型、默认值、额外信息
                            column_name = row[0]
                            column_type = row[1].lower()

                            self.columns.append(column_name)
                            self.column_types[column_name] = column_type

                        logger.info(f"MySQL连接: 获取到 {len(self.columns)} 列")
                    else:
                        logger.warning("MySQL连接: 未获取到列信息")
            finally:
                conn.close()
                logger.debug("MySQL连接已关闭")

        except Exception as e:
            logger.warning(f"MySQL连接获取表结构失败: {str(e)}")
            raise


class DataConfig:
    """数据配置类，集中管理所有预定义数据"""

    # 基础数据
    PROVINCES = [
        "北京",
        "上海",
        "天津",
        "重庆",
        "广东",
        "江苏",
        "浙江",
        "四川",
        "湖北",
        "湖南",
        "河北",
        "河南",
        "山东",
        "山西",
        "陕西",
        "安徽",
        "江西",
        "福建",
        "云南",
        "贵州",
        "广西",
        "甘肃",
        "青海",
        "内蒙古",
        "宁夏",
        "新疆",
        "辽宁",
        "吉林",
        "黑龙江",
        "西藏",
        "海南",
        "香港",
        "澳门",
        "台湾",
    ]
    ISPS = ["电信", "联通", "移动", "教育网", "广电网"]
    COUNTRIES = [
        "中国",
        "美国",
        "英国",
        "德国",
        "法国",
        "日本",
        "韩国",
        "俄罗斯",
        "加拿大",
        "澳大利亚",
    ]
    CDN_VENDORS = [
        "阿里云",
        "腾讯云",
        "华为云",
        "百度云",
        "金山云",
        "网宿",
        "七牛云",
        "又拍云",
        "帝联",
        "快网",
    ]

    # 应用相关数据
    APP_NAMES = [
        "live-streaming",
        "video-player",
        "music-app",
        "game-client",
        "news-reader",
        "social-media",
        "e-commerce",
        "education-platform",
        "finance-app",
        "health-monitor",
        "weather-service",
        "map-navigation",
        "chat-messenger",
        "video-conference",
        "file-sharing",
        "photo-gallery",
        "shopping-mall",
        "travel-booking",
        "food-delivery",
        "taxi-service",
        "fitness-tracker",
        "meditation-app",
        "podcast-player",
        "radio-streaming",
        "tv-broadcast",
    ]
    STREAM_NAMES = [
        "main-channel",
        "backup-stream",
        "hd-quality",
        "mobile-stream",
        "low-latency",
        "live-event",
        "sports-broadcast",
        "news-feed",
        "music-radio",
        "podcast-stream",
        "game-stream",
        "education-live",
        "conference-room",
        "webinar-hall",
        "concert-hall",
        "movie-theater",
        "tv-channel",
        "radio-station",
        "audio-book",
        "voice-chat",
        "video-call",
        "screen-share",
        "file-transfer",
        "data-sync",
        "backup-channel",
    ]
    STREAM_PROTOCOLS = [
        "RTMP",
        "RTSP",
        "HLS",
        "DASH",
        "WebRTC",
        "SRT",
        "UDP",
        "TCP",
        "HTTP",
        "HTTPS",
    ]

    # URI相关数据
    URI_PATHS = [
        "/api/v1/stream",
        "/live/channel",
        "/video/play",
        "/audio/stream",
        "/media/content",
        "/hls/playlist.m3u8",
        "/dash/manifest.mpd",
        "/rtmp/live",
        "/webrtc/offer",
        "/srt/stream",
        "/api/upload",
        "/api/download",
        "/api/status",
        "/api/health",
        "/api/metrics",
        "/content/video",
        "/content/audio",
        "/content/image",
        "/content/document",
        "/content/archive",
        "/user/profile",
        "/user/settings",
        "/user/history",
        "/user/favorites",
        "/user/playlist",
        "/admin/dashboard",
        "/admin/users",
        "/admin/content",
        "/admin/analytics",
        "/admin/logs",
    ]
    QUERY_PARAMS = [
        "quality=hd",
        "quality=sd",
        "quality=4k",
        "format=mp4",
        "format=flv",
        "format=ts",
        "bitrate=1000",
        "bitrate=2000",
        "bitrate=5000",
        "fps=30",
        "fps=60",
        "fps=25",
        "token=abc123",
        "session=xyz789",
        "user_id=12345",
        "channel_id=67890",
        "start_time=0",
        "duration=3600",
        "autoplay=true",
        "mute=false",
        "loop=true",
    ]

    # 网络相关数据
    PRIVATE_IP_RANGES = [
        "192.168.",
        "10.",
        "172.16.",
        "172.17.",
        "172.18.",
        "172.19.",
        "172.20.",
        "172.21.",
        "172.22.",
        "172.23.",
        "172.24.",
        "172.25.",
        "172.26.",
        "172.27.",
        "172.28.",
        "172.29.",
        "172.30.",
        "172.31.",
    ]
    PUBLIC_IP_PREFIXES = [
        "114.114.",
        "8.8.",
        "223.5.",
        "180.76.",
        "61.135.",
        "202.108.",
        "119.29.",
        "182.254.",
        "47.95.",
        "39.156.",
        "106.11.",
        "123.125.",
    ]

    # HTTP状态码（按常见程度加权）
    HTTP_CODES = (
        [200] * 8
        + [201, 204] * 2
        + [301, 302, 304] * 2
        + [400, 401, 403] * 2
        + [404] * 4
        + [500] * 2
        + [502, 503, 504]
    )

    # 其他数据
    DATA_PLATFORMS = [0, 1, 2, 3, 4]

    DOMAINS = [
        "a11.yanyiyics.xin",
        "adstuic.yanyiyics.xin",
        "afafnuc.yanyiyics.xin",
        "aflcuic.yanyiyics.xin",
        "ajlivbrbwzrlc.yanyiyics.xin",
        "ajnlqjc.yanyiyics.xin",
        "alnjgavsslrrc.yanyiyics.xin",
        "aoqwnuc.yanyiyics.xin",
        "aosxxfiuiiqqc.yanyiyics.xin",
        "arcrzaikgxbcc.yanyiyics.xin",
        "atynfcc.yanyiyics.xin",
        "axbttjpjrcyfc.yanyiyics.xin",
        "axvpdxc.yanyiyics.xin",
        "bhlpzk.yanyiyics.xin",
        "bioagqc.yanyiyics.xin",
        "bjrmnmerxzlwc.yanyiyics.xin",
        "bjsapxc.yanyiyics.xin",
        "bpkyinc.yanyiyics.xin",
        "bpltfx.yanyiyics.xin",
        "bvdryzc.yanyiyics.xin",
        "bzpedic.yanyiyics.xin",
        "cetrrsc.yanyiyics.xin",
        "ceyofuc.yanyiyics.xin",
        "cixwmec.yanyiyics.xin",
        "cjcheyc.yanyiyics.xin",
        "cmypuvc.yanyiyics.xin",
        "cnmgsrc.yanyiyics.xin",
        "cpxiwhc.yanyiyics.xin",
        "cvijwgyewqxpc.yanyiyics.xin",
        "dckqkyc.yanyiyics.xin",
        "dddhnqc.yanyiyics.xin",
        "dhzwlrwmlgecc.yanyiyics.xin",
        "djnvumkoiyvkc.yanyiyics.xin",
        "dmdtftc.yanyiyics.xin",
        "dmzuqmuajgihc.yanyiyics.xin",
        "dnrptqcjcheyc.yanyiyics.xin",
        "dqpbtmybtrcbc.yanyiyics.xin",
        "dtejfvqkuimgc.yanyiyics.xin",
        "duvbmtpqrttkc.yanyiyics.xin",
        "dwsufac.yanyiyics.xin",        
    ]
    # 中域名
    MIDDLE_DOMAINS = ["a.yanyiyics.xin","b.yanyiyics.xin","c.yanyiyics.xin","d.yanyiyics.xin","testplay.gyclound.space","testpull.gyclound.space","testpush.gyclound.space",]
    # 大域名
    BIG_DOMAINS = ["skywalking.xin"]


class FieldGenerator:
    """字段生成器类，负责生成各种类型的字段值"""

    def __init__(self):
        self.faker = Faker(locale="zh_CN")
        self.request_id_pool = self._init_request_id_pool()

    def _init_request_id_pool(self) -> List[str]:
        """初始化request_id池"""
        pool = []
        for i in range(100):
            if i < 30:  # UUID格式 (30%)
                rid = f"{random.randint(10000000, 99999999)}-{random.randint(1000, 9999)}-{random.randint(1000, 9999)}-{random.randint(1000, 9999)}-{random.randint(100000000000, ************)}"
            elif i < 60:  # 时间戳+随机数格式 (30%)
                timestamp = int(
                    datetime.datetime.now().timestamp() * 1000
                ) + random.randint(-86400000, 86400000)
                rid = f"{timestamp}_{random.randint(1000, 9999)}"
            elif i < 80:  # 简单数字格式 (20%)
                rid = str(random.randint(1000000000000000, ************9999))
            else:  # 字母数字混合格式 (20%)
                chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
                rid = "REQ_" + "".join(random.choices(chars, k=12))
            pool.append(rid)
        return pool

    def generate_request_id(self) -> str:
        """生成request_id，70%概率从池中选择"""
        if random.random() < 0.7:
            return random.choice(self.request_id_pool)

        # 30%概率生成新的
        format_type = random.randint(1, 4)
        if format_type == 1:
            return f"{random.randint(10000000, 99999999)}-{random.randint(1000, 9999)}-{random.randint(1000, 9999)}-{random.randint(1000, 9999)}-{random.randint(100000000000, ************)}"
        elif format_type == 2:
            timestamp = int(
                datetime.datetime.now().timestamp() * 1000
            ) + random.randint(-86400000, 86400000)
            return f"{timestamp}_{random.randint(1000, 9999)}"
        elif format_type == 3:
            return str(random.randint(1000000000000000, ************9999))
        else:
            chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
            return "REQ_" + "".join(random.choices(chars, k=12))

    def generate_request_uri(self) -> str:
        """生成请求URI"""
        base_path = random.choice(DataConfig.URI_PATHS)
        if random.random() < 0.3:  # 30%概率添加查询参数
            num_params = random.randint(1, 3)
            selected_params = random.sample(
                DataConfig.QUERY_PARAMS, min(num_params, len(DataConfig.QUERY_PARAMS))
            )
            query_string = "&".join(selected_params)
            return f"{base_path}?{query_string}"
        return base_path

    def generate_ip_address(self, is_client: bool = True) -> str:
        """生成IP地址"""
        if is_client and random.random() < 0.8:  # 客户端80%内网IP
            prefix = random.choice(DataConfig.PRIVATE_IP_RANGES)
            if prefix == "10.":
                return f"10.{random.randint(0, 255)}.{random.randint(0, 255)}.{random.randint(1, 254)}"
            elif prefix.startswith("192.168."):
                return f"192.168.{random.randint(0, 255)}.{random.randint(1, 254)}"
            else:
                return f"{prefix}{random.randint(0, 255)}.{random.randint(1, 254)}"
        elif not is_client:  # 服务端主要内网IP
            prefix = random.choice(DataConfig.PRIVATE_IP_RANGES)
            if prefix == "10.":
                return f"10.{random.randint(0, 10)}.{random.randint(0, 255)}.{random.randint(1, 254)}"
            elif prefix.startswith("192.168."):
                return f"192.168.{random.randint(0, 10)}.{random.randint(1, 254)}"
            else:
                return f"{prefix}{random.randint(0, 255)}.{random.randint(1, 254)}"
        else:  # 公网IP
            prefix = random.choice(DataConfig.PUBLIC_IP_PREFIXES)
            return f"{prefix}{random.randint(0, 255)}.{random.randint(1, 254)}"

    def generate_domain(self) -> str:
        """生成域名"""
        rand_val = random.random()
        if rand_val < 0.4:
            return random.choice(DataConfig.BIG_DOMAINS)
        elif rand_val < 0.7:
            return random.choice(DataConfig.MIDDLE_DOMAINS)
        elif rand_val < 0.9:
            return random.choice(DataConfig.DOMAINS)
        else:
            return self.faker.domain_name()

    def generate_datetime(self) -> str:
        """按概率生成当前时间或前几个小时内的时间"""
        now = datetime.datetime.now()
        rand = random.random()
        # 95% 概率返回当前整点小时
        base_time = now - datetime.timedelta(hours=30,minutes=random.randint(1,20), seconds=random.randint(20,40))
        return base_time.strftime("%Y-%m-%d %H:%M:%S")

    def generate_date(self) -> str:
        """生成日期"""
        today = datetime.datetime.now()
        if random.random() < 0.8:
            return random.choice([today, today - datetime.timedelta(days=1)]).strftime(
                "%Y-%m-%d"
            )
        else:
            days_ago = random.randint(2, 30)
            return (today - datetime.timedelta(days=days_ago)).strftime("%Y-%m-%d")


class CSVGenerator:
    """CSV生成器类，用于生成符合Doris表结构的CSV数据"""

    def __init__(self, table_info: DorisTableInfo):
        self.table_info = table_info
        self.field_generator = FieldGenerator()
        self.fk = Faker(locale="en_US")
        # 数据类型生成器映射
        self.type_generators = {
            "string": lambda: self.fk.word(),
            "varchar": lambda: self.fk.word(),
            "char": lambda: self.fk.word(),
            "text": lambda: " ".join(self.fk.words(nb=random.randint(2, 5))),
            "int": lambda: random.randint(1, 10000),
            "integer": lambda: random.randint(1, 10000),
            "tinyint": lambda: random.randint(1, 127),
            "smallint": lambda: random.randint(1, 10000),
            "bigint": lambda: random.randint(1, 1000000),
            "float": lambda: round(random.uniform(0.1, 1000.0), 6),
            "double": lambda: round(random.uniform(0.1, 1000.0), 12),
            "decimal": lambda: round(random.uniform(0.1, 1000.0), 4),
            "boolean": lambda: random.choice([True, False]),
            "bool": lambda: random.choice([True, False]),
            "date": self.field_generator.generate_date,
            "datetime": self.field_generator.generate_datetime,
            "timestamp": self.field_generator.generate_datetime,
        }

    def _get_special_field_value(
        self, column: str, current_request_id: str, window_start: str
    ) -> Any:
        """获取特殊字段的值"""
        column_lower = column.lower()

        # 特殊字段映射
        special_fields = {
            "request_id": lambda: current_request_id
            or self.field_generator.generate_request_id(),
            "unique_request": lambda: current_request_id
            or self.field_generator.generate_request_id(),
            "request_uri": lambda: self.fk.uri_path(),
            "request_count": lambda: 1,
            "app_name": lambda: random.choice(DataConfig.APP_NAMES),
            "round": lambda: random.randint(0, 20),
            "end": lambda: random.choice([0, 1]),
            "stream_name": lambda: random.choice(DataConfig.STREAM_NAMES),
            "client_ip": lambda: self.field_generator.generate_ip_address(
                is_client=True
            ),
            "service_ip": lambda: self.field_generator.generate_ip_address(
                is_client=False
            ),
            "stream_protocol": lambda: random.choice(DataConfig.STREAM_PROTOCOLS),
            "tenant_id": lambda: random.randint(1000001, 1000050),
            "region": lambda: random.choice(["cn", "eu", "an"]),
            "billing_region": lambda: random.choice(["cn", "eu", "an"]),
            "domain": lambda: self.field_generator.generate_domain(),
            "isp": lambda: random.choice(DataConfig.ISPS),
            "province": lambda: random.choice(DataConfig.PROVINCES),
            "country": lambda: random.choice(DataConfig.COUNTRIES),
            "cdn_vendor": lambda: random.choice(DataConfig.CDN_VENDORS),
            "http_code": lambda: random.choice(DataConfig.HTTP_CODES),
            "data_platform": lambda: random.choice(DataConfig.DATA_PLATFORMS),
            "round": lambda: random.randint(0, 20),
            "end": lambda: random.choice([0, 1]),
            "window_start": lambda: window_start,
            "window_end": lambda: self._generate_window_end(window_start),
            "cur_timestamp": lambda: datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "user_agent": lambda: self.fk.user_agent(),
            "request_methods": lambda: self.fk.http_method(),
            "http_method": lambda: self.fk.http_method(),
            "internal": lambda: 0
        }

        # 检查精确匹配
        if column_lower in special_fields:
            return special_fields[column_lower]()

        # 检查包含匹配
        for key, generator in special_fields.items():
            if key in column_lower:
                return generator()

        # 检查别名匹配
        aliases = {
            "uri": "request_uri",
            "url": "request_uri",
            "count": "request_count",
            "app": "app_name",
            "application": "app_name",
            "stream": "stream_name",
            "clientip": "client_ip",
            "serviceip": "service_ip",
            "client_addr": "client_ip",
            "service_addr": "service_ip",
            "server_ip": "service_ip",
            "protocol": "stream_protocol",
            "status_code": "http_code",
            "http_status": "http_code",
            "platform_id": "data_platform",
        }

        for alias, real_key in aliases.items():
            if column_lower == alias and real_key in special_fields:
                return special_fields[real_key]()

        return None

    def _generate_window_end(self, window_start: str) -> str:
        """生成window_end字段"""
        if window_start:
            try:
                start_time = datetime.datetime.strptime(
                    window_start, "%Y-%m-%d %H:%M:%S"
                )
                seconds_to_add = random.randint(45, 55)
                end_time = start_time + datetime.timedelta(seconds=seconds_to_add)
                return end_time.strftime("%Y-%m-%d %H:%M:%S")
            except Exception:
                pass
        return self.field_generator.generate_datetime()

    def generate_row(self) -> List[str]:
        """生成一行数据"""
        row = []

        # 预生成window_start（如果需要）
        window_start = None
        if any(col.lower() == "window_start" for col in self.table_info.columns):
            window_start = self.field_generator.generate_datetime()

        # 预生成request_id（如果需要）
        current_request_id = None
        if any(
            "request_id" in col.lower() or "unique_request" in col.lower()
            for col in self.table_info.columns
        ):
            current_request_id = self.field_generator.generate_request_id()

        # 为每个字段生成值
        for column in self.table_info.columns:
            column_type = self.table_info.column_types.get(column, "string").lower()
            base_type = column_type.split("(")[0]

            # 尝试获取特殊字段值
            value = self._get_special_field_value(
                column, current_request_id, window_start
            )

            # 如果不是特殊字段，使用类型生成器
            if value is None:
                if "datetime" in base_type or "timestamp" in base_type:
                    value = self.field_generator.generate_datetime()
                else:
                    generator = self.type_generators.get(
                        base_type, lambda: self.fk.word()
                    )
                    value = generator()

            row.append(str(value))

        return row

    def generate_csv(self, output_file: str, num_rows: int, delimiter: str = ","):
        """生成CSV文件"""
        try:
            # 检查是否有列信息
            if not self.table_info.columns:
                logger.error("没有列信息，无法生成CSV文件")
                raise ValueError("没有列信息，无法生成CSV文件")

            with open(output_file, "w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file, delimiter=delimiter)

                # 写入数据行
                for _ in range(num_rows):
                    row = self.generate_row()
                    writer.writerow(row)

            logger.info(f"成功生成 {num_rows} 行数据到文件 {output_file}")

            # 显示文件的前几行
            self._preview_csv(output_file, delimiter, 1)

            return output_file

        except Exception as e:
            logger.error(f"生成CSV文件时出错: {str(e)}")
            raise

    def _preview_csv(self, file_path, delimiter=",", num_lines=5):
        """预览CSV文件的前几行"""
        try:
            logger.info(f"预览文件 {file_path} 的前 {num_lines} 行:")
            with open(file_path, "r", encoding="utf-8") as f:
                for i, line in enumerate(f):
                    if i >= num_lines:
                        break
                    # 显示行号和内容
                    logger.info(f"行 {i+1}: {line.strip()}")

                    # 如果是CSV格式，尝试解析并显示每个字段
                    try:
                        fields = line.strip().split(delimiter)
                        field_info = []
                        for j, field in enumerate(fields):
                            if j < len(self.table_info.columns):
                                column_name = self.table_info.columns[j]
                                field_info.append(f"{column_name}={field}")
                            else:
                                field_info.append(f"field{j+1}={field}")
                        logger.info(f"  解析: {', '.join(field_info)}")
                    except Exception:
                        pass  # 忽略解析错误
        except Exception as e:
            logger.error(f"预览文件时出错: {str(e)}")


class DorisStreamLoader:
    """Doris Stream Load工具类，用于将CSV数据导入Doris"""

    def __init__(self, table_info: DorisTableInfo):
        self.table_info = table_info

    def _build_columns_parameter(self) -> str:
        """构建columns参数"""
        # 检查是否有unique_request字段
        has_unique_request = any(
            "unique_request" in col.lower() for col in self.table_info.columns
        )

        if has_unique_request:
            # 使用临时列方式处理unique_request字段
            # 格式：col1,col2,temp_unique_request,unique_request=bitmap_hash64(temp_unique_request)
            temp_columns = []
            final_mappings = []

            for column in self.table_info.columns:
                if "unique_request" in column.lower():
                    # 为unique_request字段使用临时列
                    temp_col = f"temp_{column}"
                    temp_columns.append(temp_col)
                    final_mappings.append(f"{column}=bitmap_hash64({temp_col})")
                else:
                    # 普通字段直接使用原列名
                    temp_columns.append(column)

            # 组合：临时列定义 + 目标列映射
            columns_str = ",".join(temp_columns + final_mappings)
            logger.info(
                f"检测到unique_request字段，使用bitmap_hash64转换: {columns_str}"
            )
            return columns_str
        else:
            return ",".join(self.table_info.columns)

    def load_csv(
        self, csv_file: str, delimiter: str = ",", max_filter_ratio: float = 0.0
    ):
        """使用Stream Load导入CSV文件到Doris"""
        try:
            # 检查文件是否存在
            if not os.path.exists(csv_file):
                raise FileNotFoundError(f"文件不存在: {csv_file}")

            # 构建Stream Load URL
            url = f"http://{self.table_info.host}:{self.table_info.port}/api/{self.table_info.database}/{self.table_info.table}/_stream_load"

            # 设置Basic认证
            auth = base64.b64encode(
                f"{self.table_info.user}:{self.table_info.password}".encode()
            ).decode()
            auth_header = f"Basic {auth}"

            # 生成唯一的label
            label = f"csv_import_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 构建columns参数
            columns_str = self._build_columns_parameter()

            # 设置请求头
            headers = {
                "Authorization": auth_header,
                "Expect": "100-continue",
                "Content-Type": "text/plain; charset=UTF-8",
                "label": label,
                "columns": columns_str,
                "column_separator": delimiter,
                "max_filter_ratio": str(max_filter_ratio),
                "format": "csv",
                "strict_mode": "false",  # 关闭严格模式，允许一些数据类型转换
                "timezone": "Asia/Shanghai",  # 设置时区
            }

            logger.info(f"设置最大过滤比例: {max_filter_ratio}")

            # 如果max_filter_ratio大于0，添加一些调试信息
            if max_filter_ratio > 0:
                logger.info(f"允许最多 {int(max_filter_ratio * 100)}% 的行被过滤")
                logger.info(
                    "这意味着如果有数据类型不匹配或违反约束条件的行，只要不超过这个比例，导入仍然会成功"
                )

            # 读取CSV文件内容
            with open(csv_file, "rb") as f:
                csv_data = f.read()

            # 发送Stream Load请求
            logger.info(f"开始向 {url} 发送Stream Load请求...")
            logger.info(f"使用的columns参数: {columns_str}")
            logger.debug(f"请求头: {headers}")

            # 不使用Session，手动处理重定向
            response = requests.put(
                url, headers=headers, data=csv_data, allow_redirects=False
            )

            # 如果是重定向，手动处理
            if response.status_code in (301, 302, 303, 307, 308):
                redirect_url = response.headers.get("Location")
                logger.info(f"收到重定向到: {redirect_url}")

                if redirect_url:
                    # 在重定向请求中保留原始的Authorization头
                    logger.info(f"向重定向URL发送请求: {redirect_url}")
                    response = requests.put(
                        redirect_url, headers=headers, data=csv_data
                    )

            # 解析响应
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("Status") == "Success":
                        logger.info(f"导入成功: {result}")
                        return True, result
                    else:
                        logger.error(f"导入失败: {result}")
                        return False, result
                except ValueError:
                    logger.error(f"无法解析JSON响应: {response.text}")
                    return False, {
                        "Status": "Failed",
                        "Message": f"无法解析响应: {response.text[:100]}...",
                    }
            else:
                logger.error(f"请求失败: {response.status_code} - {response.text}")
                return False, {
                    "Status": "Failed",
                    "Message": f"HTTP错误: {response.status_code}",
                }

        except Exception as e:
            logger.error(f"导入数据时出错: {str(e)}")
            return False, {"Status": "Failed", "Message": str(e)}


def generate_and_import_batch(
    table_info,
    batch_size,
    batch_number,
    total_batches,
    output_file,
    delimiter,
    max_filter_ratio,
):
    """生成并导入一个批次的数据"""
    # 生成批次特定的输出文件名
    batch_output_file = f"{os.path.splitext(output_file)[0]}_batch{batch_number}{os.path.splitext(output_file)[1]}"

    logger.info(
        f"开始生成批次 {batch_number}/{total_batches}，每批次 {batch_size} 行..."
    )

    # 生成CSV文件
    generator = CSVGenerator(table_info)
    csv_file = generator.generate_csv(batch_output_file, batch_size, delimiter)

    # 导入数据
    logger.info(f"开始导入批次 {batch_number}/{total_batches} 到Doris...")
    loader = DorisStreamLoader(table_info)
    success, result = loader.load_csv(csv_file, delimiter, max_filter_ratio)

    if success:
        logger.info(f"批次 {batch_number}/{total_batches} 导入成功!")
        # 显示导入统计信息
        if isinstance(result, dict):
            logger.info(f"导入总行数: {result.get('NumberTotalRows', 'N/A')}")
            logger.info(f"成功导入行数: {result.get('NumberLoadedRows', 'N/A')}")
            logger.info(f"过滤行数: {result.get('NumberFilteredRows', 'N/A')}")
            logger.info(f"导入耗时: {result.get('LoadTimeMs', 'N/A')} ms")
    else:
        logger.error(f"批次 {batch_number}/{total_batches} 导入失败: {result}")
        # 如果有详细错误信息，显示出来
        if isinstance(result, dict) and "Message" in result:
            logger.error(f"错误信息: {result['Message']}")

        # 提供一些常见错误的解决方案
        if isinstance(result, dict) and "Message" in result:
            message = result["Message"]
            if "NOT_AUTHORIZED" in message:
                logger.error(
                    "认证失败。请检查用户名和密码是否正确，以及用户是否有导入权限。"
                )
            elif "table not exists" in message.lower():
                logger.error("表不存在。请检查数据库名和表名是否正确。")
            elif "column not exists" in message.lower():
                logger.error("列不存在。请检查CSV文件的列名是否与表结构匹配。")
            elif "type not match" in message.lower():
                logger.error("类型不匹配。请检查CSV文件的数据类型是否与表结构匹配。")
            elif "DATA_QUALITY_ERROR" in message:
                logger.error(
                    "数据质量错误。有些行被过滤掉了，可能是因为数据类型不匹配或者违反了表的约束条件。"
                )
                logger.error(
                    f"总行数: {result.get('NumberTotalRows', 'N/A')}, 过滤行数: {result.get('NumberFilteredRows', 'N/A')}"
                )
                logger.error(
                    "尝试增加 --max-filter-ratio 参数的值，例如 --max-filter-ratio 0.2"
                )

                # 尝试获取错误日志的详细信息
                if "ErrorURL" in result:
                    error_url = result["ErrorURL"]
                    logger.info(f"尝试获取错误详情: {error_url}")
                    try:
                        error_response = requests.get(error_url)
                        if error_response.status_code == 200:
                            logger.error("错误详情:")
                            error_lines = error_response.text.strip().split("\n")
                            for i, line in enumerate(error_lines[:10]):  # 只显示前10行
                                logger.error(f"  {i+1}. {line}")
                            if len(error_lines) > 10:
                                logger.error(
                                    f"  ... 还有 {len(error_lines) - 10} 行未显示"
                                )
                    except Exception as e:
                        logger.error(f"获取错误详情失败: {str(e)}")

    # 清理临时文件
    try:
        if os.path.exists(csv_file):
            os.remove(csv_file)
            logger.info(f"已删除临时文件: {csv_file}")
    except Exception as e:
        logger.warning(f"删除临时文件失败: {str(e)}")

    return success


def main():
    parser = argparse.ArgumentParser(
        description="根据Doris表生成CSV并使用Stream Load导入"
    )

    # Doris连接参数
    parser.add_argument("--host", type=str, required=True, help="Doris FE主机地址")
    parser.add_argument(
        "--port", type=int, default=8030, help="Doris HTTP端口，默认8030"
    )
    parser.add_argument(
        "--mysql-port", type=int, default=9030, help="Doris MySQL端口，默认9030"
    )
    parser.add_argument(
        "--user", type=str, default="root", help="Doris用户名，默认root"
    )
    parser.add_argument("--password", type=str, default="", help="Doris密码")
    parser.add_argument("--database", type=str, required=True, help="Doris数据库名")
    parser.add_argument("--table", type=str, required=True, help="Doris表名")

    # CSV生成参数
    parser.add_argument(
        "--output",
        type=str,
        default="doris_data.csv",
        help="输出CSV文件路径，默认doris_data.csv",
    )
    parser.add_argument(
        "--rows", type=int, default=1000, help="生成的数据行数，默认1000"
    )
    parser.add_argument(
        "--delimiter", type=str, default=",", help="CSV分隔符，默认逗号"
    )

    # 自定义列参数
    parser.add_argument(
        "--columns", type=str, help='自定义列名，逗号分隔，例如"id,name,age"'
    )
    parser.add_argument(
        "--types", type=str, help='自定义列类型，逗号分隔，例如"int,string,int"'
    )

    # 导入参数
    parser.add_argument(
        "--import", dest="do_import", action="store_true", help="生成后立即导入"
    )
    parser.add_argument(
        "--no-import", dest="do_import", action="store_false", help="仅生成不导入"
    )
    parser.add_argument(
        "--max-filter-ratio", type=float, default=0.1, help="最大过滤比例，默认0.1"
    )
    parser.set_defaults(do_import=True)

    # 批次参数
    parser.add_argument(
        "--batch-size", type=int, default=0, help="每个批次的行数，默认0表示不分批"
    )
    parser.add_argument(
        "--batch-number",
        type=int,
        default=0,
        help="要生成的批次编号，从1开始，默认0表示生成所有批次",
    )
    parser.add_argument("--total-batches", type=int, default=1, help="总批次数，默认1")

    # 调试参数
    parser.add_argument("--debug", action="store_true", help="启用更详细的调试输出")

    args = parser.parse_args()

    # 如果启用调试模式，设置日志级别为DEBUG
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("已启用调试模式")

    try:
        # 获取表结构信息
        table_info = DorisTableInfo(
            args.host,
            args.port,
            args.user,
            args.password,
            args.database,
            args.table,
            args.mysql_port,
        )

        # 如果提供了自定义列和类型，则使用自定义的
        if args.columns and args.types:
            custom_columns = [col.strip() for col in args.columns.split(",")]
            custom_types = [typ.strip() for typ in args.types.split(",")]

            if len(custom_columns) != len(custom_types):
                logger.warning("自定义列名和类型数量不匹配，将忽略自定义设置")
            else:
                logger.info(f"使用自定义列结构: {args.columns}")
                table_info.columns = custom_columns
                table_info.column_types = {
                    col: typ for col, typ in zip(custom_columns, custom_types)
                }

        # 如果没有获取到列信息，给出明确的错误
        if not table_info.columns:
            logger.error(
                "无法获取表结构，也没有提供有效的自定义列。请检查连接参数或使用--columns和--types参数提供自定义列。"
            )
            return

        # 处理批次参数
        if args.batch_size > 0:
            # 分批次处理
            batch_size = args.batch_size
            total_batches = args.total_batches

            if args.batch_number > 0:
                # 只生成指定批次
                if args.batch_number > total_batches:
                    logger.error(
                        f"指定的批次编号 {args.batch_number} 超出了总批次数 {total_batches}"
                    )
                    return

                logger.info(f"将只生成并导入批次 {args.batch_number}/{total_batches}")
                if args.do_import:
                    generate_and_import_batch(
                        table_info,
                        batch_size,
                        args.batch_number,
                        total_batches,
                        args.output,
                        args.delimiter,
                        args.max_filter_ratio,
                    )
                else:
                    # 只生成不导入
                    batch_output_file = f"{os.path.splitext(args.output)[0]}_batch{args.batch_number}{os.path.splitext(args.output)[1]}"
                    generator = CSVGenerator(table_info)
                    generator.generate_csv(
                        batch_output_file, batch_size, args.delimiter
                    )
            else:
                # 生成所有批次
                logger.info(
                    f"将生成并导入 {total_batches} 个批次，每批次 {batch_size} 行"
                )

                success_count = 0
                for batch_number in range(1, total_batches + 1):
                    if args.do_import:
                        success = generate_and_import_batch(
                            table_info,
                            batch_size,
                            batch_number,
                            total_batches,
                            args.output,
                            args.delimiter,
                            args.max_filter_ratio,
                        )
                        if success:
                            success_count += 1
                    else:
                        # 只生成不导入
                        batch_output_file = f"{os.path.splitext(args.output)[0]}_batch{batch_number}{os.path.splitext(args.output)[1]}"
                        generator = CSVGenerator(table_info)
                        generator.generate_csv(
                            batch_output_file, batch_size, args.delimiter
                        )
                        success_count += 1

                if args.do_import:
                    logger.info(
                        f"所有批次处理完成，成功: {success_count}/{total_batches}"
                    )
                else:
                    logger.info(
                        f"所有批次生成完成，成功: {success_count}/{total_batches}"
                    )
        else:
            # 不分批次，直接处理
            # 生成CSV文件
            generator = CSVGenerator(table_info)
            csv_file = generator.generate_csv(args.output, args.rows, args.delimiter)

            # 如果需要导入
            if args.do_import:
                logger.info("开始导入数据到Doris...")
                loader = DorisStreamLoader(table_info)
                success, result = loader.load_csv(
                    csv_file, args.delimiter, args.max_filter_ratio
                )

                if success:
                    logger.info("数据导入成功!")
                    # 显示导入统计信息
                    if isinstance(result, dict):
                        logger.info(
                            f"导入总行数: {result.get('NumberTotalRows', 'N/A')}"
                        )
                        logger.info(
                            f"成功导入行数: {result.get('NumberLoadedRows', 'N/A')}"
                        )
                        logger.info(
                            f"过滤行数: {result.get('NumberFilteredRows', 'N/A')}"
                        )
                        logger.info(f"导入耗时: {result.get('LoadTimeMs', 'N/A')} ms")
                else:
                    logger.error(f"数据导入失败: {result}")
                    # 如果有详细错误信息，显示出来
                    if isinstance(result, dict) and "Message" in result:
                        logger.error(f"错误信息: {result['Message']}")

                    # 提供一些常见错误的解决方案
                    if isinstance(result, dict) and "Message" in result:
                        message = result["Message"]
                        if "NOT_AUTHORIZED" in message:
                            logger.error(
                                "认证失败。请检查用户名和密码是否正确，以及用户是否有导入权限。"
                            )
                        elif "table not exists" in message.lower():
                            logger.error("表不存在。请检查数据库名和表名是否正确。")
                        elif "column not exists" in message.lower():
                            logger.error(
                                "列不存在。请检查CSV文件的列名是否与表结构匹配。"
                            )
                        elif "type not match" in message.lower():
                            logger.error(
                                "类型不匹配。请检查CSV文件的数据类型是否与表结构匹配。"
                            )
                        elif "DATA_QUALITY_ERROR" in message:
                            logger.error(
                                "数据质量错误。有些行被过滤掉了，可能是因为数据类型不匹配或者违反了表的约束条件。"
                            )
                            logger.error(
                                f"总行数: {result.get('NumberTotalRows', 'N/A')}, 过滤行数: {result.get('NumberFilteredRows', 'N/A')}"
                            )
                            logger.error(
                                "尝试增加 --max-filter-ratio 参数的值，例如 --max-filter-ratio 0.2"
                            )

                            # 尝试获取错误日志的详细信息
                            if "ErrorURL" in result:
                                error_url = result["ErrorURL"]
                                logger.info(f"尝试获取错误详情: {error_url}")
                                try:
                                    error_response = requests.get(error_url)
                                    if error_response.status_code == 200:
                                        logger.error("错误详情:")
                                        error_lines = error_response.text.strip().split(
                                            "\n"
                                        )
                                        for i, line in enumerate(
                                            error_lines[:10]
                                        ):  # 只显示前10行
                                            logger.error(f"  {i+1}. {line}")
                                        if len(error_lines) > 10:
                                            logger.error(
                                                f"  ... 还有 {len(error_lines) - 10} 行未显示"
                                            )
                                except Exception as e:
                                    logger.error(f"获取错误详情失败: {str(e)}")

    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")


if __name__ == "__main__":
    main()
