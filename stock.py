
from datetime import date, timedelta
import tkinter as tk

import baostock as bs
import pandas as pd
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.pyplot as plt

#### 登陆系统 ####
lg = bs.login()
# 显示登陆返回信息
print('login respond error_code:'+lg.error_code)
print('login respond  error_msg:'+lg.error_msg)

s01="date,code,high,low,volume,amount"
s02="date,code,high,low,volume,amount"
s03="date,time,code,high,low,volume,amount"
s0=s01
#### 获取沪深A股历史K线数据 ####
# 详细指标参数，参见“历史行情指标参数”章节；“分钟线”参数与“日线”参数不同。“分钟线”不包含指数。
# 分钟线指标：date,time,code,open,high,low,close,volume,amount,adjustflag
# 周月线指标：date,code,open,high,low,close,volume,amount,adjustflag,turn,pctChg
rs = bs.query_history_k_data_plus("sh.600000",
    "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
    start_date='2025-03-03', end_date='2025-03-04',
    frequency="d", adjustflag="3")
print('query_history_k_data_plus respond error_code:'+rs.error_code)
print('query_history_k_data_plus respond  error_msg:'+rs.error_msg)

#### 打印结果集 ####
data_list = []
while (rs.error_code == '0') & rs.next():
    # 获取一条记录，将记录合并在一起
    data_list.append(rs.get_row_data())
result = pd.DataFrame(data_list, columns=rs.fields)
x=result

#### 结果集输出到csv文件 ####   
#result.to_csv("D:\\history_A_stock_k_data-001.csv", index=False)
print(result)

#tk
root = tk.Tk()
root.geometry("800x600")  # 设置初始大小

menubar = tk.Menu(root)
vlang = tk.StringVar()
# 每次打印出当前选中的
def printitem():
    print('vlang = {}'.format(vlang.get()))
    ss1=vlang.get()
    print(ss1)
    e1.delete(0, "end")
    e1.insert(0, ss1)

filemenu = tk.Menu(menubar, tearoff=0)
for i in ['sh.600000', 'sh.600000', 'sh.600000', 'sh.600000', 'sh.600000']:
    # 绑定变量与回调函数，指定的变量vlang 将这几项划为一组
    filemenu.add_radiobutton(label=i, command=printitem, variable=vlang)

# 将menubar的 menu 属性指定为 filemenu，即 filemenu 为 menubar 的下拉菜单
menubar.add_cascade(label='股票', menu = filemenu)
#root['menu'] = menubar

#menubar2 = tk.Menu(root)
vlang2 = tk.StringVar()
# 每次打印出当前选中的
def printitem2():
    print('vlang2 = {}'.format(vlang2.get()))
    ss2d =vlang2.get()
    #date_object2 = date.fromisoformat(ss2d) 
    print(ss2d)
    e2.delete(0, "end")
    e2.insert(0, ss2d)

filemenu2 = tk.Menu(menubar, tearoff=0)
for i in ['2025-03-03', '2025-03-04', '2025-03-05', '2025-03-06', '2025-03-07']:
    # 绑定变量与回调函数，指定的变量vlang 将这几项划为一组
    filemenu2.add_radiobutton(label=i, command=printitem2, variable=vlang2)

# 将menubar的 menu 属性指定为 filemenu，即 filemenu 为 menubar 的下拉菜单
menubar.add_cascade(label='日期1', menu = filemenu2)
#root['menu'] = menubar

#menubar3 = tk.Menu(root)
vlang3 = tk.StringVar()
# 每次打印出当前选中的
def printitem3():
    print('vlang3 = {}'.format(vlang3.get()))
    ss3d =vlang3.get()
    #date_object3 = date.fromisoformat(ss3d) 
    print(ss3d)
    e3.delete(0, "end")
    e3.insert(0, ss3d)

filemenu3 = tk.Menu(menubar, tearoff=0)
for i in ['2025-03-03', '2025-03-04', '2025-03-05', '2025-03-06', '2025-03-07']:
    # 绑定变量与回调函数，指定的变量vlang 将这几项划为一组
    filemenu3.add_radiobutton(label=i, command=printitem3, variable=vlang3)

# 将menubar的 menu 属性指定为 filemenu，即 filemenu 为 menubar 的下拉菜单
menubar.add_cascade(label='日期2', menu = filemenu3)
#root['menu'] = menubar

#menubar4 = tk.Menu(root)
vlang4 = tk.StringVar()
# 每次打印出当前选中的
def printitem4():
    print('vlang4 = {}'.format(vlang4.get()))
    ss4 =vlang4.get()
    print(ss4)
    e4.delete(0, "end")
    e4.insert(0, ss4)
    if ss4 == "d":
        s0=s01
    elif ss4 == "w":
        s0=s02
    elif ss4 == "m":
        s0=s02
    elif ss4 == "5":
        s0=s03
    elif ss4 == "15":
        s0=s03
    elif ss4 == "30":
        s0=s03
    elif ss4 == "60":
        s0=s03
    else:
        s0=s01
    print(s0)
    e0.delete(0, "end")
    e0.insert(0, s0)

#默认为d，日k线；d=日k线、w=周、m=月、5=5分钟、15=15分钟、30=30分钟、60=60分钟k线数据
filemenu4 = tk.Menu(menubar, tearoff=0)
for i in ['d', 'w', 'm', '5', '15', '30', '60']:
    # 绑定变量与回调函数，指定的变量vlang 将这几项划为一组
    filemenu4.add_radiobutton(label=i, command=printitem4, variable=vlang4)

# 将menubar的 menu 属性指定为 filemenu，即 filemenu 为 menubar 的下拉菜单
menubar.add_cascade(label='选项', menu = filemenu4)
#root['menu'] = menubar

#默认为d，日k线；d=日k线、w=周、m=月、5=5分钟、15=15分钟、30=30分钟、60=60分钟k线数据
#menubar5 = tk.Menu(root)
vlang5 = tk.StringVar()
# 每次打印出当前选中的
def printitem5():
    global x  # 声明 x 为全局变量
    global y  # 声明 x 为全局变量
    ss1=e1.get()
    ss2d=e2.get()
    ss3d=e3.get()
    ss4=e4.get()
    s0=e0.get()
    print(s0)
    print(ss1,ss2d,ss3d,ss4)
    rs = bs.query_history_k_data_plus(ss1,s0,
    start_date=ss2d, end_date=ss3d,
    frequency=ss4, adjustflag="3")
    print('query_history_k_data_plus respond error_code:'+rs.error_code)
    print('query_history_k_data_plus respond  error_msg:'+rs.error_msg)
    #### 打印结果集 ####
    data_list = []
    while (rs.error_code == '0') & rs.next():
        # 获取一条记录，将记录合并在一起
        data_list.append(rs.get_row_data())
    result = pd.DataFrame(data_list, columns=rs.fields)
    x=result
    #### 结果集输出到csv文件 ####   
    #result.to_csv("D:\\history_A_stock_k_data-001.csv", index=False)
    print(result)
    result['high'] = pd.to_numeric(result['high'], errors='coerce')
    result['low'] = pd.to_numeric(result['low'], errors='coerce')
    result['amount'] = pd.to_numeric(result['amount'], errors='coerce')
    result['volume'] = pd.to_numeric(result['volume'], errors='coerce')
    result['m']=result['amount']/result['volume']
    x=result
    print(result)
    text.delete("1.0", "end")
    text.insert("end", result)
    n=len(result.index)
    n1 = list(range(1, n+1))
    y = pd.DataFrame(n1, columns=['i'])  # 指定列名
    y['n']=x['date']
    y['high']=x['high']
    y['low']=x['low']
    y['volume']=x['volume']
    y['amount']=x['amount']
    y['m']=x['m']
    print(y)

filemenu5 = tk.Menu(menubar, tearoff=0)
for i in ['查看']:
    # 绑定变量与回调函数，指定的变量vlang 将这几项划为一组
    filemenu5.add_radiobutton(label=i, command=printitem5, variable=vlang5)

# 将menubar的 menu 属性指定为 filemenu，即 filemenu 为 menubar 的下拉菜单
menubar.add_cascade(label='查看', menu = filemenu5)
#root['menu'] = menubar

#menubar51 = tk.Menu(root)
vlang51 = tk.StringVar()
# 每次打印出当前选中的
def printitem51():
    global y  # 声明 x 为全局变量
    root2 = tk.Tk()
    root2.title("Example")
    # 创建一个Figure对象
    fig, ax = plt.subplots(2, 1)
    # 绘制第一条曲线
    ax[0].plot(y['n'], y['high'], label='high')
    # 绘制第二条曲线
    ax[0].plot(y['n'], y['low'], label='low')
    ax[0].plot(y['n'], y['m'], label='m')
    # 添加图例
    ax[0].legend()
    # 绘制第一条曲线
    ax[1].plot(y['n'], y['volume'], label='volume')
    # 绘制第二条曲线
    ax[1].plot(y['n'], y['amount'], label='amount')
    # 添加图例
    ax[1].legend()
    # 创建FigureCanvasTkAgg对象，并将其添加到Tkinter窗口中
    canvas = FigureCanvasTkAgg(fig, master=root2)  
    canvas.draw()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
    root2.mainloop()

filemenu51 = tk.Menu(menubar, tearoff=0)
for i in ['查看图片']:
    # 绑定变量与回调函数，指定的变量vlang 将这几项划为一组
    filemenu51.add_radiobutton(label=i, command=printitem51, variable=vlang51)

# 将menubar的 menu 属性指定为 filemenu，即 filemenu 为 menubar 的下拉菜单
menubar.add_cascade(label='查看图片', menu = filemenu51)
#root['menu'] = menubar

#menubar52 = tk.Menu(root)
vlang52 = tk.StringVar()
# 每次打印出当前选中的
def printitem52():
    global x  # 声明 x 为全局变量
    result=x
    print('vlang52 = {}'.format(vlang51.get()))
    #### 保存数据 ####
    result.to_csv("D:\\history_A_stock_k_data-003.csv", index=False)
    print(result)

filemenu52 = tk.Menu(menubar, tearoff=0)
for i in ['保存数据']:
    # 绑定变量与回调函数，指定的变量vlang 将这几项划为一组
    filemenu52.add_radiobutton(label=i, command=printitem52, variable=vlang52)

# 将menubar的 menu 属性指定为 filemenu，即 filemenu 为 menubar 的下拉菜单
menubar.add_cascade(label='保存数据', menu = filemenu52)
#root['menu'] = menubar

#menubar55 = tk.Menu(root)
vlang55 = tk.StringVar()
# 每次打印出当前选中的
def printitem55():
    print('vlang55 = {}'.format(vlang55.get()))
    #### 登出系统 ####
    bs.logout()
    root.destroy()

filemenu55 = tk.Menu(menubar, tearoff=0)
for i in ['退出']:
    # 绑定变量与回调函数，指定的变量vlang 将这几项划为一组
    filemenu55.add_radiobutton(label=i, command=printitem55, variable=vlang55)

# 将menubar的 menu 属性指定为 filemenu，即 filemenu 为 menubar 的下拉菜单
menubar.add_cascade(label='退出', menu = filemenu55)
root['menu'] = menubar

menubar6 = tk.Menu(root)
vlang6 = tk.StringVar()
# 每次打印出当前选中的
def printitem6():
    print('vlang6 = {}'.format(vlang6.get()))
    ss6i = int( vlang6.get() )
    today = date.today()
    todayq6 = date.today() + timedelta(days=ss6i)
    print( todayq6)
    ss2d=str(todayq6)
    print(ss2d)
    e2.delete(0, "end")
    e2.insert(0, ss2d)

filemenu6 = tk.Menu(menubar6, tearoff=0)
for i in ['0', '-1', '-2', '-3', '-4']:
    # 绑定变量与回调函数，指定的变量vlang 将这几项划为一组
    filemenu6.add_radiobutton(label=i, command=printitem6, variable=vlang6)
    # 将各个菜单项使用分隔符隔开
    filemenu6.add_separator()

# 将menubar的 menu 属性指定为 filemenu，即 filemenu 为 menubar 的下拉菜单
menubar6.add_cascade(label='日期01', menu=filemenu6)

#menubar7 = tk.Menu(root)
vlang7 = tk.StringVar()
# 每次打印出当前选中的
def printitem7():
    print('vlang7 = {}'.format(vlang7.get()))
    ss7i = int( vlang7.get() )
    today = date.today()
    todayq7 = date.today() + timedelta(days=ss7i)
    print( todayq7)
    ss3d=str(todayq7)
    print(ss3d)
    e3.delete(0, "end")
    e3.insert(0, ss3d)

filemenu7 = tk.Menu(menubar6, tearoff=0)
for i in ['0', '-1', '-2', '-3', '-4']:
    # 绑定变量与回调函数，指定的变量vlang 将这几项划为一组
    filemenu7.add_radiobutton(label=i, command=printitem7, variable=vlang7)
    # 将各个菜单项使用分隔符隔开
    filemenu7.add_separator()

# 将menubar的 menu 属性指定为 filemenu，即 filemenu 为 menubar 的下拉菜单
menubar6.add_cascade(label='日期02', menu=filemenu7)

# 此时就不要将 root 的 menu 设置为 menubar 了
# root['menu'] = menubar6
def popup(event):
    #显示菜单
    menubar6.post(event.x_root, event.y_root)
# 在这里相应鼠标的右键事件，右击时调用 popup,此时与菜单绑定的是 root，可以设置为
# 其它的控件，在绑定的控件上右击就可以弹出菜单
root.bind('<Button-3>', popup)
 
e1 = tk.Entry(root)
e1.pack(padx=20)
e1.delete(0, "end")
e1.insert(0, "sh.600000")
ss1="sh.600000"

e2 = tk.Entry(root)
e2.pack(padx=20)
e2.delete(0, "end")
e2.insert(0, "2025-03-03")
ss2d="2025-03-03"

e3 = tk.Entry(root)
e3.pack(padx=20)
e3.delete(0, "end")
e3.insert(0, "2025-03-07")
ss3d="2025-03-07"

e4 = tk.Entry(root)
e4.pack(padx=20)
e4.delete(0, "end")
e4.insert(0, "d")
ss4="d"

e0 = tk.Entry(root, width=90)
e0.pack(pady=5)
e0.delete(0, "end")
e0.insert(0, "date,code,high,low,volume,amount")
s0="date,code,high,low,volume,amount"

# 创建Text小部件
text = tk.Text(root, height=10, width=40)
text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
 
# 创建Scrollbar小部件
scrollbar = tk.Scrollbar(root)
scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
 
# 将Scrollbar与Text的垂直滚动条关联起来
text['yscrollcommand'] = scrollbar.set
scrollbar['command'] = text.yview

text.delete("1.0", "end")
text.insert("end", "TEXT text")

root.mainloop()
